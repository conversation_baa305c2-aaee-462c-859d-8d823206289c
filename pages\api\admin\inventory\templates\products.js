import { Parser } from 'json2csv';

/**
 * API endpoint for downloading product import template
 * Returns a CSV template with sample data and proper headers
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - CSV template file
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Sample product data for template
    const sampleData = [
      {
        name: 'Sample Product 1',
        description: 'This is a sample product description',
        short_description: 'Short description for sample product',
        sku: 'SAMPLE-001',
        price: 29.99,
        sale_price: 24.99,
        cost_price: 15.00,
        category: 'sample-category',
        stock: 100,
        low_stock_threshold: 10,
        image_url: 'https://example.com/image1.jpg',
        gallery_images: 'https://example.com/gallery1.jpg;https://example.com/gallery2.jpg',
        status: 'active',
        featured: true,
        is_active: true,
        meta_title: 'Sample Product 1 - SEO Title',
        meta_description: 'SEO description for sample product 1'
      },
      {
        name: 'Sample Product 2',
        description: 'Another sample product description',
        short_description: 'Short description for second sample',
        sku: 'SAMPLE-002',
        price: 49.99,
        sale_price: '',
        cost_price: 25.00,
        category: 'another-category',
        stock: 50,
        low_stock_threshold: 5,
        image_url: 'https://example.com/image2.jpg',
        gallery_images: '',
        status: 'active',
        featured: false,
        is_active: true,
        meta_title: 'Sample Product 2 - SEO Title',
        meta_description: 'SEO description for sample product 2'
      }
    ];

    // Define CSV fields
    const csvFields = [
      'name', 'description', 'short_description', 'sku', 'price', 'sale_price', 
      'cost_price', 'category', 'stock', 'low_stock_threshold', 'image_url', 
      'gallery_images', 'status', 'featured', 'is_active', 'meta_title', 
      'meta_description'
    ];

    // Generate CSV
    const json2csvParser = new Parser({ fields: csvFields });
    const csv = json2csvParser.parse(sampleData);

    // Set headers for file download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="products_import_template.csv"');
    
    return res.status(200).send(csv);

  } catch (error) {
    console.error('Error generating product template:', error);
    return res.status(500).json({
      error: 'Failed to generate template',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}
