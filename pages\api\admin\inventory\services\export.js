import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { Parser } from 'json2csv';

/**
 * API endpoint for exporting services data
 * Supports CSV and JSON formats
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response or file download
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request
  const { authorized, error, user, role } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  try {
    const { format = 'csv', category, status, featured } = req.query;

    // Validate format
    if (!['csv', 'json'].includes(format)) {
      return res.status(400).json({ error: 'Invalid format. Supported formats: csv, json' });
    }

    // Build query
    let query = supabaseAdmin
      .from('services')
      .select(`
        id,
        name,
        description,
        duration,
        price,
        color,
        category,
        category_id,
        image_url,
        status,
        featured,
        visible_on_public,
        visible_on_pos,
        visible_on_events,
        meta_title,
        meta_description,
        booking_requirements,
        availability_notes,
        created_at,
        updated_at
      `)
      .order('name');

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (featured === 'true') {
      query = query.eq('featured', true);
    } else if (featured === 'false') {
      query = query.eq('featured', false);
    }

    // Execute query
    const { data: services, error: queryError } = await query;

    if (queryError) {
      console.error('Error fetching services for export:', queryError);
      return res.status(500).json({ error: 'Failed to fetch services data' });
    }

    // Process data for export
    const exportData = services.map(service => ({
      id: service.id,
      name: service.name || '',
      description: service.description || '',
      duration: service.duration || 0,
      price: service.price || 0,
      color: service.color || '#6a0dad',
      category: service.category || '',
      category_id: service.category_id || '',
      image_url: service.image_url || '',
      status: service.status || 'active',
      featured: service.featured || false,
      visible_on_public: service.visible_on_public !== false,
      visible_on_pos: service.visible_on_pos !== false,
      visible_on_events: service.visible_on_events !== false,
      meta_title: service.meta_title || '',
      meta_description: service.meta_description || '',
      booking_requirements: service.booking_requirements || '',
      availability_notes: service.availability_notes || '',
      created_at: service.created_at,
      updated_at: service.updated_at
    }));

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `services_export_${timestamp}`;

    if (format === 'json') {
      // Return JSON format with proper headers
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      return res.status(200).json(exportData);
    } else {
      // Return CSV format with proper headers
      const csvFields = [
        'id', 'name', 'description', 'duration', 'price', 'color', 'category',
        'category_id', 'image_url', 'status', 'featured', 'visible_on_public',
        'visible_on_pos', 'visible_on_events', 'meta_title', 'meta_description',
        'booking_requirements', 'availability_notes', 'created_at', 'updated_at'
      ];

      const json2csvParser = new Parser({ fields: csvFields });
      const csv = json2csvParser.parse(exportData);

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      return res.status(200).send(csv);
    }

  } catch (error) {
    console.error('Error exporting services:', error);
    return res.status(500).json({
      error: 'Failed to export services',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}
