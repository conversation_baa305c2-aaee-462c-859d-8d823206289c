import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import ProductList from '@/components/admin/ProductList';
import ServiceList from '@/components/admin/ServiceList';
import Modal from '@/components/admin/Modal';
import InventoryDashboard from '@/components/admin/inventory/InventoryDashboard';
import ProductForm from '@/components/admin/inventory/ProductForm';
import ServiceForm from '@/components/admin/inventory/ServiceForm';
import StockAdjustmentForm from '@/components/admin/inventory/StockAdjustmentForm';
import StockMovementLog from '@/components/admin/inventory/StockMovementLog';
import ErrorBoundary from '@/components/ErrorBoundary';
import { authenticatedFetch } from '@/lib/auth-utils';
// Console error capture removed for production security
import styles from '@/styles/admin/InventoryPage.module.css';

export default function InventoryPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('dashboard'); // 'dashboard', 'products', 'services', 'movements'
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(null); // 'add-product', 'edit-product', 'add-service', 'edit-service', 'adjust-stock'
  const [selectedItem, setSelectedItem] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [error, setError] = useState(null);

  // Add comprehensive client-side error monitoring
  useEffect(() => {
    const originalError = console.error;
    const originalWarn = console.warn;

    // Enhanced error detection
    console.error = (...args) => {
      const errorMessage = args.join(' ');
      console.log('🔍 Console.error intercepted:', errorMessage);

      if (errorMessage.includes('Objects are not valid as a React child') ||
          errorMessage.includes('Element type is invalid') ||
          errorMessage.includes('Cannot read property') ||
          errorMessage.includes('Cannot read properties of undefined') ||
          errorMessage.includes('Minified React error #130') ||
          errorMessage.includes('Error: Minified React error #130')) {
        console.log('🚨 REACT ERROR #130 DETECTED IN INVENTORY PAGE:', errorMessage);
        setError(`React Error #130 detected: ${errorMessage.substring(0, 200)}...`);
      }
      originalError.apply(console, args);
    };

    // Also monitor warnings (development only)
    if (process.env.NODE_ENV === 'development') {
      console.warn = (...args) => {
        const warnMessage = args.join(' ');
        console.log('⚠️ Console.warn intercepted:', warnMessage);
        originalWarn.apply(console, args);
      };
    }

    // Add global error handler
    const handleGlobalError = (event) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('🚨 Global error detected:', event.error);
      }
      if (event.error && event.error.message &&
          (event.error.message.includes('Minified React error #130') ||
           event.error.message.includes('Objects are not valid as a React child'))) {
        setError(`Global React Error #130: ${event.error.message}`);
      }
    };

    window.addEventListener('error', handleGlobalError);

    return () => {
      console.error = originalError;
      console.warn = originalWarn;
      window.removeEventListener('error', handleGlobalError);
    };
  }, []);
  const [stats, setStats] = useState({
    totalProducts: 0,
    activeProducts: 0,
    totalServices: 0,
    activeServices: 0,
  });
  const [loading, setLoading] = useState(true);

  // Fetch inventory statistics
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        console.log('Fetching inventory stats...');

        // Use authenticatedFetch for consistent authentication handling
        const data = await authenticatedFetch('/api/admin/inventory/stats', {}, {
          redirect: false, // Don't redirect on auth failure, just show error
          notify: true     // Show notifications for errors
        });

        console.log('Inventory stats received:', data);
        setStats(data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching inventory stats:', error);
        setLoading(false);

        // Set default values on error to prevent UI issues
        setStats({
          totalProducts: 0,
          activeProducts: 0,
          totalServices: 0,
          activeServices: 0,
        });
      }
    };

    fetchStats();
  }, [refreshKey]);

  // Handle URL parameters for direct editing and tab navigation
  useEffect(() => {
    // Console error capture removed for production security
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 InventoryPage: Component initialized');
      console.log('🎯 Development mode active');
    }

    if (router.isReady) {
      const { tab, edit } = router.query;

      // Set active tab if specified
      if (tab && ['dashboard', 'products', 'services', 'movements'].includes(tab)) {
        setActiveTab(tab);
        console.log(`📋 Active tab set to: ${tab}`);
      }

      // Handle edit parameter
      if (edit && tab) {
        console.log(`🔧 Edit parameter detected: ${edit} for tab: ${tab}`);
        if (tab === 'services') {
          // Fetch service data and open edit modal
          fetchServiceForEdit(edit);
        } else if (tab === 'products') {
          // Fetch product data and open edit modal
          fetchProductForEdit(edit);
        }
      }
    }
  }, [router.isReady, router.query]);

  // Update URL when tab changes
  const handleTabChange = (newTab) => {
    setActiveTab(newTab);
    // Update URL to reflect the current tab
    router.push(`/admin/inventory?tab=${newTab}`, undefined, { shallow: true });
  };

  // Fetch service for editing
  const fetchServiceForEdit = async (serviceId) => {
    try {
      const response = await fetch(`/api/admin/services/${serviceId}`);
      if (response.ok) {
        const data = await response.json();
        setSelectedItem(data.service);
        setModalType('edit-service');
        setShowModal(true);
      }
    } catch (error) {
      console.error('Error fetching service for edit:', error);
    }
  };

  // Fetch product for editing
  const fetchProductForEdit = async (productId) => {
    try {
      const response = await fetch(`/api/admin/inventory/products/${productId}`);
      if (response.ok) {
        const data = await response.json();
        setSelectedItem(data.product);
        setModalType('edit-product');
        setShowModal(true);
      }
    } catch (error) {
      console.error('Error fetching product for edit:', error);
    }
  };

  // Handle item selection for editing
  const handleSelectItem = (item, type) => {
    try {
      console.log('🔍 handleSelectItem called:', { item, type });
      console.log('🔍 Item type:', typeof item);
      console.log('🔍 Item keys:', item ? Object.keys(item) : 'null');

      // Validate item data before setting state
      if (!item || typeof item !== 'object') {
        console.error('❌ Invalid item data:', item);
        setError('Invalid item data. Please refresh the page and try again.');
        return;
      }

      // Ensure item has required properties
      if (!item.id) {
        console.error('❌ Item missing ID:', item);
        setError('Item data is incomplete. Please refresh the page and try again.');
        return;
      }

      // Check for problematic data types that could cause React Error #130
      Object.keys(item).forEach(key => {
        const value = item[key];
        const valueType = typeof value;
        console.log(`🔍 ${key}: ${valueType}`, value);

        if (valueType === 'object' && value !== null && !Array.isArray(value)) {
          console.warn(`⚠️ Found object property that might cause React Error #130: ${key}`, value);
          if (value instanceof Date) {
            console.log(`📅 Date object detected for ${key}, converting to string`);
          }
        }
      });

      console.log('✅ Item validation passed, setting modal state');
      console.log('🔍 About to call setSelectedItem...');
      setSelectedItem(item);
      console.log('✅ setSelectedItem completed');

      console.log('🔍 About to call setModalType...');
      setModalType(type === 'product' ? 'edit-product' : 'edit-service');
      console.log('✅ setModalType completed');

      console.log('🔍 About to call setShowModal...');
      setShowModal(true);
      console.log('✅ setShowModal completed');

    } catch (error) {
      console.error('❌ Error in handleSelectItem:', error);
      console.error('❌ Error stack:', error.stack);
      setError('Failed to open edit form. Please refresh the page and try again.');
    }
  };

  // Handle adding new inventory item
  const handleAddItem = (type) => {
    setSelectedItem(null);
    setModalType(type === 'product' ? 'add-product' : 'add-service');
    setShowModal(true);
  };

  // Handle saving inventory item (create or update)
  const handleSaveItem = async () => {
    // Close modal
    setShowModal(false);
    // Refresh data
    setRefreshKey(prevKey => prevKey + 1);
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowModal(false);
    setError(null); // Clear any errors when modal closes
  };

  // Handle export functionality
  const handleExport = async (type, format) => {
    setIsExporting(true);
    try {
      const endpoint = `/api/admin/inventory/${type}/export?format=${format}`;
      const response = await authenticatedFetch(endpoint, {
        method: 'GET'
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;

      // Get filename from response headers or create default
      const contentDisposition = response.headers.get('content-disposition');
      let filename = `${type}_export_${new Date().toISOString().split('T')[0]}.${format}`;
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setShowExportModal(false);
      setError(null);
    } catch (error) {
      console.error('Export error:', error);
      setError('Failed to export data. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Handle import functionality
  const handleImport = async (type) => {
    if (!importFile) {
      setError('Please select a file to import');
      return;
    }

    setIsImporting(true);
    try {
      const formData = new FormData();
      formData.append('file', importFile);
      formData.append('updateMode', importMode);

      const response = await authenticatedFetch(`/api/admin/inventory/${type}/import`, {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Import failed');
      }

      setImportResults(result.results);
      setShowImportModal(false);
      setImportFile(null);
      setError(null);

      // Refresh data
      setRefreshKey(prev => prev + 1);
      fetchStats();

    } catch (error) {
      console.error('Import error:', error);
      setError(error.message || 'Failed to import data. Please try again.');
    } finally {
      setIsImporting(false);
    }
  };

  // Handle template download
  const handleDownloadTemplate = async (type) => {
    try {
      const response = await authenticatedFetch(`/api/admin/inventory/templates/${type}`, {
        method: 'GET'
      });

      if (!response.ok) {
        throw new Error('Failed to download template');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type}_import_template.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

    } catch (error) {
      console.error('Template download error:', error);
      setError('Failed to download template. Please try again.');
    }
  };

  // Render modal content based on modalType
  const renderModalContent = () => {
    try {
      console.log('🔍 renderModalContent called:', { modalType, selectedItem });

      switch (modalType) {
        case 'add-product':
          return (
            <div className={styles.modalContent}>
              <ProductForm
                onSave={handleSaveItem}
                onCancel={handleCloseModal}
              />
            </div>
          );
        case 'edit-product':
          if (!selectedItem) {
            console.error('❌ No selectedItem for edit-product');
            return (
              <div className={styles.modalContent}>
                <div className={styles.error}>
                  Product data not found. Please close this dialog and try again.
                </div>
              </div>
            );
          }
          return (
            <div className={styles.modalContent}>
              <ProductForm
                product={selectedItem}
                onSave={handleSaveItem}
                onCancel={handleCloseModal}
                onDelete={() => {
                  // Close modal and refresh data
                  handleCloseModal();
                  setRefreshKey(prev => prev + 1);
                  fetchStats();
                }}
              />
            </div>
          );
        case 'adjust-stock':
          if (!selectedItem) {
            console.error('❌ No selectedItem for adjust-stock');
            return (
              <div className={styles.modalContent}>
                <div className={styles.error}>
                  Product data not found. Please close this dialog and try again.
                </div>
              </div>
            );
          }
          return (
            <div className={styles.modalContent}>
              <StockAdjustmentForm
                product={selectedItem}
                onSave={handleSaveItem}
                onCancel={handleCloseModal}
              />
            </div>
          );
        case 'add-service':
          return (
            <div className={styles.modalContent}>
              <ErrorBoundary>
                <ServiceForm
                  onSave={handleSaveItem}
                  onCancel={handleCloseModal}
                />
              </ErrorBoundary>
            </div>
          );
        case 'edit-service':
          if (!selectedItem) {
            console.error('❌ No selectedItem for edit-service');
            return (
              <div className={styles.modalContent}>
                <div className={styles.error}>
                  Service data not found. Please close this dialog and try again.
                </div>
              </div>
            );
          }

          // Validate selectedItem structure to prevent React Error #130
          try {
            if (typeof selectedItem !== 'object' || selectedItem === null) {
              throw new Error('Invalid service object structure');
            }

            // Ensure all required fields are properly serialized
            const safeSelectedItem = {
              ...selectedItem,
              name: String(selectedItem.name || ''),
              description: String(selectedItem.description || ''),
              price: String(selectedItem.price || ''),
              duration: String(selectedItem.duration || ''),
              category: String(selectedItem.category || ''),
              status: String(selectedItem.status || 'active'),
              color: String(selectedItem.color || '#6a0dad'),
              image_url: String(selectedItem.image_url || ''),
              featured: Boolean(selectedItem.featured),
              // Ensure pricing_tiers is properly serialized
              pricing_tiers: Array.isArray(selectedItem.pricing_tiers)
                ? selectedItem.pricing_tiers.map(tier => ({
                    ...tier,
                    name: String(tier.name || ''),
                    description: String(tier.description || ''),
                    price: String(tier.price || ''),
                    duration: String(tier.duration || ''),
                    is_default: Boolean(tier.is_default),
                    sort_order: Number(tier.sort_order) || 0
                  }))
                : []
            };

            console.log('✅ Rendering ServiceForm with safe selectedItem:', safeSelectedItem);
            return (
              <div className={styles.modalContent}>
                <ErrorBoundary>
                  <ServiceForm
                    service={safeSelectedItem}
                    onSave={handleSaveItem}
                    onCancel={handleCloseModal}
                    onDelete={() => {
                      // Close modal and refresh data
                      handleCloseModal();
                      setRefreshKey(prev => prev + 1);
                      fetchStats();
                    }}
                  />
                </ErrorBoundary>
              </div>
            );
          } catch (error) {
            console.error('❌ Error preparing service data for ServiceForm:', error);
            return (
              <div className={styles.modalContent}>
                <div className={styles.error}>
                  Error loading service data. Please close this dialog and try again.
                </div>
              </div>
            );
          }
        default:
          console.error('❌ Unknown modalType:', modalType);
          return (
            <div className={styles.modalContent}>
              <div className={styles.error}>
                Unknown dialog type. Please close this dialog and try again.
              </div>
            </div>
          );
      }
    } catch (error) {
      console.error('❌ Error in renderModalContent:', error);
      return (
        <div className={styles.modalContent}>
          <div className={styles.error}>
            Something went wrong loading the form. Please close this dialog and try again.
          </div>
        </div>
      );
    }
  };

  return (
    <ProtectedRoute>
      <AdminLayout title="Services & Shop">
        <div className={styles.inventoryPage}>
          {error && (
            <div className={styles.error}>
              {error}
              <button
                onClick={() => setError(null)}
                className={styles.errorCloseButton}
              >
                ×
              </button>
            </div>
          )}

          <div className={styles.header}>
            <h1>Services & Shop Management</h1>
            <div className={styles.actionButtons}>
              {activeTab === 'products' && (
                <>
                  <button
                    className={styles.addButton}
                    onClick={() => handleAddItem('product')}
                  >
                    Add Product
                  </button>
                  <button
                    className={styles.exportButton}
                    onClick={() => setShowExportModal(true)}
                  >
                    Export Products
                  </button>
                  <button
                    className={styles.importButton}
                    onClick={() => setShowImportModal(true)}
                  >
                    Import Products
                  </button>
                </>
              )}
              {activeTab === 'services' && (
                <>
                  <button
                    className={styles.addButton}
                    onClick={() => handleAddItem('service')}
                  >
                    Add Service
                  </button>
                  <button
                    className={styles.exportButton}
                    onClick={() => setShowExportModal(true)}
                  >
                    Export Services
                  </button>
                  <button
                    className={styles.importButton}
                    onClick={() => setShowImportModal(true)}
                  >
                    Import Services
                  </button>
                </>
              )}
            </div>
          </div>

          <div className={styles.statsContainer}>
            <div className={styles.statsCard}>
              <h3>Total Products</h3>
              <p className={styles.statValue}>
                {loading ? '...' : stats.totalProducts}
              </p>
            </div>
            <div className={styles.statsCard}>
              <h3>Active Products</h3>
              <p className={styles.statValue}>
                {loading ? '...' : stats.activeProducts}
              </p>
            </div>
            <div className={styles.statsCard}>
              <h3>Total Services</h3>
              <p className={styles.statValue}>
                {loading ? '...' : stats.totalServices}
              </p>
            </div>
            <div className={styles.statsCard}>
              <h3>Active Services</h3>
              <p className={styles.statValue}>
                {loading ? '...' : stats.activeServices}
              </p>
            </div>
          </div>

          <div className={styles.tabsContainer}>
            <div className={styles.tabs}>
              <button
                className={`${styles.tabButton} ${
                  activeTab === 'dashboard' ? styles.activeTab : ''
                }`}
                onClick={() => handleTabChange('dashboard')}
              >
                Dashboard
              </button>
              <button
                className={`${styles.tabButton} ${
                  activeTab === 'products' ? styles.activeTab : ''
                }`}
                onClick={() => handleTabChange('products')}
              >
                Products
              </button>
              <button
                className={`${styles.tabButton} ${
                  activeTab === 'services' ? styles.activeTab : ''
                }`}
                onClick={() => handleTabChange('services')}
              >
                Services
              </button>
              <button
                className={`${styles.tabButton} ${
                  activeTab === 'movements' ? styles.activeTab : ''
                }`}
                onClick={() => handleTabChange('movements')}
              >
                Stock Movements
              </button>
            </div>

            <div className={styles.tabContent}>
              {activeTab === 'dashboard' && (
                <InventoryDashboard />
              )}
              {activeTab === 'products' && (
                <ProductList
                  refreshKey={refreshKey}
                  onSelectProduct={(product) => handleSelectItem(product, 'product')}
                  onAdjustStock={(product) => {
                    setSelectedItem(product);
                    setModalType('adjust-stock');
                    setShowModal(true);
                  }}
                  onDeleteProduct={() => {
                    // Refresh the list after deletion
                    setRefreshKey(prev => prev + 1);
                    // Refresh stats
                    fetchStats();
                  }}
                />
              )}
              {activeTab === 'services' && (
                <ServiceList
                  refreshKey={refreshKey}
                  onSelectService={(service) => fetchServiceForEdit(service.id)}
                  onDeleteService={() => {
                    // Refresh the list after deletion
                    setRefreshKey(prev => prev + 1);
                    // Refresh stats
                    fetchStats();
                  }}
                />
              )}
              {activeTab === 'movements' && (
                <StockMovementLog />
              )}
            </div>
          </div>

          {showModal && (
            <Modal onClose={handleCloseModal}>
              {renderModalContent()}
            </Modal>
          )}

          {/* Export Modal */}
          {showExportModal && (
            <Modal onClose={() => setShowExportModal(false)}>
              <div className={styles.modalContent}>
                <h2>Export {activeTab === 'products' ? 'Products' : 'Services'}</h2>
                <div className={styles.exportOptions}>
                  <div className={styles.formGroup}>
                    <label htmlFor="exportFormat">Export Format:</label>
                    <select
                      id="exportFormat"
                      value={exportFormat}
                      onChange={(e) => setExportFormat(e.target.value)}
                      className={styles.selectInput}
                    >
                      <option value="csv">CSV</option>
                      <option value="json">JSON</option>
                    </select>
                  </div>
                  <div className={styles.modalActions}>
                    <button
                      className={styles.cancelButton}
                      onClick={() => setShowExportModal(false)}
                      disabled={isExporting}
                    >
                      Cancel
                    </button>
                    <button
                      className={styles.exportButton}
                      onClick={() => handleExport(activeTab, exportFormat)}
                      disabled={isExporting}
                    >
                      {isExporting ? 'Exporting...' : 'Export'}
                    </button>
                  </div>
                </div>
              </div>
            </Modal>
          )}

          {/* Import Modal */}
          {showImportModal && (
            <Modal onClose={() => setShowImportModal(false)}>
              <div className={styles.modalContent}>
                <h2>Import {activeTab === 'products' ? 'Products' : 'Services'}</h2>
                <div className={styles.importOptions}>
                  <div className={styles.formGroup}>
                    <label htmlFor="importFile">Select File:</label>
                    <input
                      type="file"
                      id="importFile"
                      accept=".csv,.json"
                      onChange={(e) => setImportFile(e.target.files[0])}
                      className={styles.fileInput}
                    />
                    <small className={styles.helpText}>
                      Supported formats: CSV, JSON (Max size: 10MB)
                    </small>
                  </div>
                  <div className={styles.formGroup}>
                    <button
                      type="button"
                      className={styles.templateButton}
                      onClick={() => handleDownloadTemplate(activeTab)}
                    >
                      Download Template
                    </button>
                    <small className={styles.helpText}>
                      Download a sample CSV template to see the expected format
                    </small>
                  </div>
                  <div className={styles.formGroup}>
                    <label htmlFor="importMode">Import Mode:</label>
                    <select
                      id="importMode"
                      value={importMode}
                      onChange={(e) => setImportMode(e.target.value)}
                      className={styles.selectInput}
                    >
                      <option value="create">Create Only (Skip existing)</option>
                      <option value="update">Update Only (Skip new)</option>
                      <option value="upsert">Create & Update (Upsert)</option>
                    </select>
                    <small className={styles.helpText}>
                      Create Only: Only add new items, skip existing ones<br/>
                      Update Only: Only update existing items, skip new ones<br/>
                      Create & Update: Add new items and update existing ones
                    </small>
                  </div>
                  <div className={styles.modalActions}>
                    <button
                      className={styles.cancelButton}
                      onClick={() => setShowImportModal(false)}
                      disabled={isImporting}
                    >
                      Cancel
                    </button>
                    <button
                      className={styles.importButton}
                      onClick={() => handleImport(activeTab)}
                      disabled={isImporting || !importFile}
                    >
                      {isImporting ? 'Importing...' : 'Import'}
                    </button>
                  </div>
                </div>
              </div>
            </Modal>
          )}

          {/* Import Results Modal */}
          {importResults && (
            <Modal onClose={() => setImportResults(null)}>
              <div className={styles.modalContent}>
                <h2>Import Results</h2>
                <div className={styles.importResults}>
                  <div className={styles.resultsSummary}>
                    <p><strong>Created:</strong> {importResults.created}</p>
                    <p><strong>Updated:</strong> {importResults.updated}</p>
                    <p><strong>Skipped:</strong> {importResults.skipped}</p>
                    <p><strong>Errors:</strong> {importResults.errors?.length || 0}</p>
                  </div>
                  {importResults.errors && importResults.errors.length > 0 && (
                    <div className={styles.errorsList}>
                      <h3>Errors:</h3>
                      <ul>
                        {importResults.errors.map((error, index) => (
                          <li key={index}>
                            Row {error.row}: {error.error}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <div className={styles.modalActions}>
                    <button
                      className={styles.addButton}
                      onClick={() => setImportResults(null)}
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
            </Modal>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
