import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import formidable from 'formidable';
import fs from 'fs';
import csv from 'csv-parser';
import { v4 as uuidv4 } from 'uuid';

// Disable default body parser for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

/**
 * API endpoint for importing services data
 * Supports CSV and JSON formats
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response with import results
 */
export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request
  const { authorized, error, user, role } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  try {
    // Parse form data
    const form = new formidable.IncomingForm({
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
    });

    const { fields, files } = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        resolve({ fields, files });
      });
    });

    // Check if file exists
    if (!files.file) {
      return res.status(400).json({ error: 'No file provided' });
    }

    const file = files.file;
    const updateMode = fields.updateMode?.[0] || 'create'; // 'create', 'update', 'upsert'
    
    // Validate file type
    const allowedTypes = ['text/csv', 'application/json'];
    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({ 
        error: 'Invalid file type. Only CSV and JSON files are allowed.' 
      });
    }

    let importData = [];

    // Parse file based on type
    if (file.mimetype === 'text/csv') {
      importData = await parseCSVFile(file.filepath);
    } else if (file.mimetype === 'application/json') {
      importData = await parseJSONFile(file.filepath);
    }

    // Validate import data
    const validationResult = validateServiceData(importData);
    if (!validationResult.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.errors
      });
    }

    // Process import based on update mode
    const importResult = await processServiceImport(importData, updateMode, user.id);

    // Clean up uploaded file
    fs.unlinkSync(file.filepath);

    return res.status(200).json({
      success: true,
      message: 'Services imported successfully',
      results: importResult
    });

  } catch (error) {
    console.error('Error importing services:', error);
    return res.status(500).json({
      error: 'Failed to import services',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}

/**
 * Parse CSV file and return array of objects
 */
async function parseCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });
}

/**
 * Parse JSON file and return array of objects
 */
async function parseJSONFile(filePath) {
  const fileContent = fs.readFileSync(filePath, 'utf8');
  const data = JSON.parse(fileContent);
  return Array.isArray(data) ? data : [data];
}

/**
 * Validate service data
 */
function validateServiceData(data) {
  const errors = [];
  const requiredFields = ['name', 'duration', 'price'];

  data.forEach((item, index) => {
    const rowErrors = [];

    // Check required fields
    requiredFields.forEach(field => {
      if (!item[field] || item[field].toString().trim() === '') {
        rowErrors.push(`Missing required field: ${field}`);
      }
    });

    // Validate price
    if (item.price && isNaN(parseFloat(item.price))) {
      rowErrors.push('Price must be a valid number');
    }

    // Validate duration
    if (item.duration && isNaN(parseInt(item.duration))) {
      rowErrors.push('Duration must be a valid integer (minutes)');
    }

    // Validate status
    if (item.status && !['active', 'inactive'].includes(item.status)) {
      rowErrors.push('Status must be either "active" or "inactive"');
    }

    // Validate featured
    if (item.featured && !['true', 'false', true, false].includes(item.featured)) {
      rowErrors.push('Featured must be true or false');
    }

    // Validate visibility fields
    ['visible_on_public', 'visible_on_pos', 'visible_on_events'].forEach(field => {
      if (item[field] && !['true', 'false', true, false].includes(item[field])) {
        rowErrors.push(`${field} must be true or false`);
      }
    });

    if (rowErrors.length > 0) {
      errors.push({
        row: index + 1,
        errors: rowErrors
      });
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Process service import based on update mode
 */
async function processServiceImport(data, updateMode, userId) {
  const results = {
    created: 0,
    updated: 0,
    skipped: 0,
    errors: []
  };

  for (let i = 0; i < data.length; i++) {
    const item = data[i];

    try {
      // Prepare service data
      const serviceData = {
        name: item.name?.trim(),
        description: item.description?.trim() || '',
        duration: parseInt(item.duration) || 0,
        price: parseFloat(item.price) || 0,
        color: item.color?.trim() || '#6a0dad',
        category: item.category?.trim() || '',
        category_id: item.category_id?.trim() || null,
        image_url: item.image_url?.trim() || '',
        status: item.status?.trim() || 'active',
        featured: item.featured === 'true' || item.featured === true,
        visible_on_public: item.visible_on_public !== 'false' && item.visible_on_public !== false,
        visible_on_pos: item.visible_on_pos !== 'false' && item.visible_on_pos !== false,
        visible_on_events: item.visible_on_events !== 'false' && item.visible_on_events !== false,
        meta_title: item.meta_title?.trim() || '',
        meta_description: item.meta_description?.trim() || '',
        booking_requirements: item.booking_requirements?.trim() || '',
        availability_notes: item.availability_notes?.trim() || '',
        updated_at: new Date().toISOString()
      };

      // Check if service exists (by name or ID)
      let existingService = null;
      if (item.id) {
        const { data: serviceById } = await supabaseAdmin
          .from('services')
          .select('id, name')
          .eq('id', item.id)
          .single();
        existingService = serviceById;
      } else {
        const { data: serviceByName } = await supabaseAdmin
          .from('services')
          .select('id, name')
          .eq('name', serviceData.name)
          .single();
        existingService = serviceByName;
      }

      if (existingService) {
        // Service exists
        if (updateMode === 'create') {
          results.skipped++;
          continue;
        } else if (updateMode === 'update' || updateMode === 'upsert') {
          // Update existing service
          const { error } = await supabaseAdmin
            .from('services')
            .update(serviceData)
            .eq('id', existingService.id);

          if (error) {
            results.errors.push({
              row: i + 1,
              error: `Failed to update service: ${error.message}`
            });
          } else {
            results.updated++;
          }
        }
      } else {
        // Service doesn't exist
        if (updateMode === 'update') {
          results.skipped++;
          continue;
        } else if (updateMode === 'create' || updateMode === 'upsert') {
          // Create new service
          if (!serviceData.id) {
            serviceData.id = uuidv4();
          }
          serviceData.created_at = new Date().toISOString();

          const { error } = await supabaseAdmin
            .from('services')
            .insert([serviceData]);

          if (error) {
            results.errors.push({
              row: i + 1,
              error: `Failed to create service: ${error.message}`
            });
          } else {
            results.created++;
          }
        }
      }
    } catch (error) {
      results.errors.push({
        row: i + 1,
        error: `Processing error: ${error.message}`
      });
    }
  }

  return results;
}
